import { useState, useEffect, useCallback } from "react";
import { useEscapeKey } from "@/hooks/useEscapeKey";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import {
  ArrowLeft,
  Users,
  Clock,
  Plus,
  CheckCircle,
  AlertCircle,
  Coffee,
  Utensils,
  Trash2,
  MoreVertical
} from "lucide-react";
import { useNavigate } from "react-router-dom";
import { Logo } from "@/components/ui/logo";
import { kotStorage } from "@/utils/kotStorage";
import { useToast } from "@/hooks/use-toast";

const AdminTableManagement = () => {
  const navigate = useNavigate();
  const { toast } = useToast();

  // Load tables from localStorage or use default
  const loadTables = () => {
    try {
      const stored = localStorage.getItem('restaurant_tables');
      if (stored) {
        return JSON.parse(stored);
      }
    } catch (error) {
      console.error('Error loading tables:', error);
    }
    // Default tables
    return [
      { id: "1", capacity: 4, status: "available", orderNumber: null },
      { id: "2", capacity: 2, status: "available", orderNumber: null },
      { id: "3", capacity: 6, status: "available", orderNumber: null },
      { id: "4", capacity: 4, status: "available", orderNumber: null },
      { id: "5", capacity: 2, status: "available", orderNumber: null },
      { id: "6", capacity: 4, status: "available", orderNumber: null },
      { id: "7", capacity: 8, status: "available", orderNumber: null },
      { id: "8", capacity: 4, status: "available", orderNumber: null },
    ];
  };

  const [tables, setTables] = useState(loadTables);
  const [isAddTableDialogOpen, setIsAddTableDialogOpen] = useState(false);
  const [newTableCapacity, setNewTableCapacity] = useState("4");
  const [newTableNumber, setNewTableNumber] = useState("");
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [tableToDelete, setTableToDelete] = useState<any>(null);
  const [currentInput, setCurrentInput] = useState("");

  // Global Escape key handler for back navigation
  useEscapeKey("/admin/dashboard", !isAddTableDialogOpen && !currentInput);

  // Save tables to localStorage
  const saveTables = (updatedTables: any[]) => {
    try {
      localStorage.setItem('restaurant_tables', JSON.stringify(updatedTables));
      setTables(updatedTables);
    } catch (error) {
      console.error('Error saving tables:', error);
    }
  };

  // Handle keyboard shortcuts
  const handleKeyDown = useCallback((event: KeyboardEvent) => {
    // Don't handle shortcuts if dialog is open or user is typing in an input
    if (isAddTableDialogOpen || event.target instanceof HTMLInputElement || event.target instanceof HTMLTextAreaElement) {
      return;
    }

    // Alt + N to open add table dialog (avoiding Ctrl+N browser conflict)
    if (event.altKey && event.key === 'n') {
      event.preventDefault();
      setIsAddTableDialogOpen(true);
      return;
    }

    // Escape key to clear input (back navigation handled by useEscapeKey hook)
    if (event.key === 'Escape') {
      if (currentInput) {
        setCurrentInput("");
      }
      return;
    }

    // Number keys for table navigation
    if (event.key >= '0' && event.key <= '9') {
      setCurrentInput(prev => prev + event.key);
      return;
    }

    // Enter key to navigate to table
    if (event.key === 'Enter') {
      if (currentInput) {
        const tableNumber = currentInput;
        const table = tables.find(t => t.id === tableNumber);
        if (table) {
          handleTableClick(table);
        } else {
          toast({
            title: "Table Not Found",
            description: `Table ${tableNumber} does not exist`,
            variant: "destructive",
          });
        }
        setCurrentInput("");
      }
      return;
    }

    // Delete/Del key to delete table
    if ((event.key === 'Delete' || event.key === 'Del') && currentInput) {
      const tableNumber = currentInput;
      const table = tables.find(t => t.id === tableNumber);
      if (table) {
        if (table.status === "available") {
          // Direct deletion without confirmation
          const updatedTables = tables.filter(t => t.id !== tableNumber);
          saveTables(updatedTables);
          toast({
            title: "Table Deleted",
            description: `Table ${tableNumber} has been deleted successfully`,
          });
        } else {
          toast({
            title: "Cannot Delete Table",
            description: `Table ${tableNumber} is currently ${table.status} and cannot be deleted`,
            variant: "destructive",
          });
        }
      } else {
        toast({
          title: "Table Not Found",
          description: `Table ${tableNumber} does not exist`,
          variant: "destructive",
        });
      }
      setCurrentInput("");
      return;
    }

    // Backspace to remove last digit
    if (event.key === 'Backspace') {
      setCurrentInput(prev => prev.slice(0, -1));
      return;
    }
  }, [currentInput, tables, toast, navigate, isAddTableDialogOpen]);

  // Add keyboard event listeners
  useEffect(() => {
    document.addEventListener('keydown', handleKeyDown);
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [handleKeyDown]);

  // Update table statuses based on active KOTs
  useEffect(() => {
    const updateTableStatuses = () => {
      const updatedTables = tables.map(table => {
        const activeKOT = kotStorage.getActiveKOTForTable(table.id);
        if (activeKOT) {
          return {
            ...table,
            status: "occupied",
            orderNumber: activeKOT.kotNumber,
            orderTime: new Date(activeKOT.createdAt).toLocaleTimeString('en-IN', {
              hour: '2-digit',
              minute: '2-digit',
              hour12: true
            }),
            guests: activeKOT.items.length // Using items count as guest count for now
          };
        }
        return { ...table, status: "available", orderNumber: null };
      });
      setTables(updatedTables);
    };

    updateTableStatuses();
    // Refresh every 30 seconds to check for updates
    const interval = setInterval(updateTableStatuses, 30000);
    return () => clearInterval(interval);
  }, []);

  const getStatusColor = (status: string) => {
    switch (status) {
      case "available": return "bg-white border-2 border-gray-300 text-gray-900";
      case "occupied": return "bg-red-500 text-white";
      case "preparing": return "bg-yellow-500 text-white";
      default: return "bg-gray-400 text-white";
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case "available": return "Free";
      case "occupied": return "Occupied";
      case "preparing": return "Preparing";
      default: return status;
    }
  };

  const stats = {
    total: tables.length,
    available: tables.filter(t => t.status === "available").length,
    occupied: tables.filter(t => t.status === "occupied").length,
    preparing: tables.filter(t => t.status === "preparing").length,
  };

  // Add new table
  const handleAddTable = () => {
    const capacity = parseInt(newTableCapacity);
    if (capacity < 1 || capacity > 20) {
      toast({
        title: "Invalid Capacity",
        description: "Table capacity must be between 1 and 20 seats",
        variant: "destructive",
      });
      return;
    }

    // Use specified table number or generate next available
    let tableId = newTableNumber.trim();
    if (!tableId) {
      tableId = (Math.max(...tables.map(t => parseInt(t.id))) + 1).toString();
    }

    // Check if table already exists
    const existingTable = tables.find(t => t.id === tableId);
    if (existingTable) {
      toast({
        title: "Table Already Exists",
        description: `Table ${tableId} already exists. Please choose a different number.`,
        variant: "destructive",
      });
      return;
    }

    const newTable = {
      id: tableId,
      capacity: capacity,
      status: "available",
      orderNumber: null
    };

    const updatedTables = [...tables, newTable];
    saveTables(updatedTables);

    setIsAddTableDialogOpen(false);
    setNewTableCapacity("4");
    setNewTableNumber("");

    toast({
      title: "Table Added",
      description: `Table ${tableId} with ${capacity} seats has been added successfully`,
    });
  };

  // Delete table
  const handleDeleteTable = (table: any, event: React.MouseEvent) => {
    event.stopPropagation(); // Prevent table click

    // Check if table is occupied
    if (table.status === "occupied" || table.status === "preparing") {
      toast({
        title: "Cannot Delete Table",
        description: "Cannot delete a table that is currently occupied or has orders in preparation",
        variant: "destructive",
      });
      return;
    }

    setTableToDelete(table);
    setIsDeleteDialogOpen(true);
  };

  const confirmDeleteTable = () => {
    if (!tableToDelete) return;

    const updatedTables = tables.filter(t => t.id !== tableToDelete.id);
    saveTables(updatedTables);

    setIsDeleteDialogOpen(false);
    setTableToDelete(null);

    toast({
      title: "Table Deleted",
      description: `Table ${tableToDelete.id} has been deleted successfully`,
    });
  };

  const handleTableClick = (table: any) => {
    if (table.status === "available") {
      // Go directly to order taking for available tables
      navigate(`/admin/take-order?table=${table.id}&dine-in=true`);
    } else if (table.status === "occupied" || table.status === "preparing") {
      // Go to table details for occupied tables
      navigate(`/admin/table-details/${table.id}`);
    }
  };

  return (
    <div className="apk-page-container bg-gray-50 min-h-screen" style={{paddingTop: '80px'}}>
      {/* Header */}
      <div className="bg-white shadow-sm border-b apk-header-fixed" style={{zIndex: 1000}}>
        <div className="max-w-7xl mx-auto px-3 sm:px-4 py-3">
          <div className="flex items-center gap-2 sm:gap-3">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => navigate("/admin/dashboard")}
              className="flex items-center gap-1 sm:gap-2 shrink-0"
            >
              <ArrowLeft className="h-4 w-4" />
              <span className="hidden sm:inline">Back</span>
            </Button>
            <Logo className="h-6 sm:h-8 shrink-0" />
            <div className="min-w-0 flex-1">
              <h1 className="text-lg sm:text-xl font-bold text-gray-900 truncate">Table Management</h1>
              <div className="flex items-center gap-2">
                <p className="text-xs sm:text-sm text-gray-600 truncate">
                  Number+Enter: Go to table • Number+Del: Delete table • Alt+N: Add table • Esc: Back
                </p>
                {currentInput && (
                  <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs font-mono">
                    Table: {currentInput}
                  </span>
                )}
              </div>
            </div>
            <Button
              onClick={() => setIsAddTableDialogOpen(true)}
              className="flex items-center gap-2 bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600 text-white shadow-lg hover:shadow-xl transition-all duration-300 px-4 py-2 rounded-xl font-semibold text-sm"
            >
              <Plus className="h-4 w-4" />
              <span className="hidden sm:inline">Add New Table</span>
              <span className="sm:hidden">Add</span>
            </Button>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto p-2 sm:p-3 space-y-3 sm:space-y-4">

        {/* Stats Cards */}
        <div className="grid grid-cols-2 lg:grid-cols-4 gap-2 sm:gap-3">
          <Card className="bg-gradient-to-br from-blue-500 via-blue-600 to-blue-700 text-white shadow-xl hover:shadow-2xl transition-all duration-300 hover:scale-105 border-0 overflow-hidden relative">
            <div className="absolute top-0 right-0 w-16 h-16 bg-white/10 rounded-full -translate-y-8 translate-x-8"></div>
            <CardContent className="p-3 sm:p-4 text-center relative z-10">
              <div className="text-xl sm:text-2xl font-bold mb-1">{stats.total}</div>
              <div className="text-xs font-medium uppercase tracking-wide text-blue-100">Total Tables</div>
            </CardContent>
          </Card>
          <Card className="bg-gradient-to-br from-green-500 via-green-600 to-green-700 text-white shadow-xl hover:shadow-2xl transition-all duration-300 hover:scale-105 border-0 overflow-hidden relative">
            <div className="absolute top-0 right-0 w-16 h-16 bg-white/10 rounded-full -translate-y-8 translate-x-8"></div>
            <CardContent className="p-3 sm:p-4 text-center relative z-10">
              <div className="text-xl sm:text-2xl font-bold mb-1">{stats.available}</div>
              <div className="text-xs font-medium uppercase tracking-wide text-green-100">Available</div>
            </CardContent>
          </Card>
          <Card className="bg-gradient-to-br from-red-500 via-red-600 to-red-700 text-white shadow-xl hover:shadow-2xl transition-all duration-300 hover:scale-105 border-0 overflow-hidden relative">
            <div className="absolute top-0 right-0 w-16 h-16 bg-white/10 rounded-full -translate-y-8 translate-x-8"></div>
            <CardContent className="p-3 sm:p-4 text-center relative z-10">
              <div className="text-xl sm:text-2xl font-bold mb-1">{stats.occupied}</div>
              <div className="text-xs font-medium uppercase tracking-wide text-red-100">Occupied</div>
            </CardContent>
          </Card>
          <Card className="bg-gradient-to-br from-yellow-500 via-yellow-600 to-yellow-700 text-white shadow-xl hover:shadow-2xl transition-all duration-300 hover:scale-105 border-0 overflow-hidden relative">
            <div className="absolute top-0 right-0 w-16 h-16 bg-white/10 rounded-full -translate-y-8 translate-x-8"></div>
            <CardContent className="p-3 sm:p-4 text-center relative z-10">
              <div className="text-xl sm:text-2xl font-bold mb-1">{stats.preparing}</div>
              <div className="text-xs font-medium uppercase tracking-wide text-yellow-100">Preparing</div>
            </CardContent>
          </Card>
        </div>

        {/* Legend */}
        <div className="flex items-center justify-center gap-4 sm:gap-6 bg-gradient-to-r from-gray-50 to-blue-50 rounded-xl p-3 shadow-md border border-gray-100">
          <div className="flex items-center gap-3">
            <div className="w-4 h-4 sm:w-5 sm:h-5 rounded-lg border-2 border-gray-400 bg-white shadow-sm"></div>
            <span className="text-sm font-semibold text-gray-700">Free</span>
          </div>
          <div className="flex items-center gap-3">
            <div className="w-4 h-4 sm:w-5 sm:h-5 rounded-lg bg-gradient-to-br from-red-500 to-red-600 shadow-sm"></div>
            <span className="text-sm font-semibold text-gray-700">Occupied</span>
          </div>
          <div className="flex items-center gap-3">
            <div className="w-4 h-4 sm:w-5 sm:h-5 rounded-lg bg-gradient-to-br from-yellow-500 to-yellow-600 shadow-sm"></div>
            <span className="text-sm font-semibold text-gray-700">Preparing</span>
          </div>
        </div>

        {/* Tables Grid */}
        <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-2 sm:gap-3">
          {tables.map((table) => (
            <Card
              key={table.id}
              className={`
                cursor-pointer transition-all duration-300 hover:scale-105 shadow-xl hover:shadow-2xl border-0 relative overflow-hidden
                ${table.status === "available" ? "bg-gradient-to-br from-white via-gray-50 to-blue-50 hover:from-blue-50 hover:to-blue-100" : ""}
                ${table.status === "occupied" ? "bg-gradient-to-br from-red-500 via-red-600 to-red-700 text-white" : ""}
                ${table.status === "preparing" ? "bg-gradient-to-br from-yellow-500 via-yellow-600 to-yellow-700 text-white" : ""}
              `}
              onClick={() => handleTableClick(table)}
            >
              {/* Decorative Elements */}
              <div className="absolute top-0 right-0 w-16 h-16 bg-white/10 rounded-full -translate-y-8 translate-x-8"></div>
              <div className="absolute bottom-0 left-0 w-12 h-12 bg-white/5 rounded-full translate-y-6 -translate-x-6"></div>

              {/* Delete Button - Only show for available tables */}
              {table.status === "available" && (
                <Button
                  variant="ghost"
                  size="sm"
                  className="absolute top-2 right-2 h-8 w-8 p-0 hover:bg-red-100 hover:text-red-600 z-20 rounded-full shadow-md bg-white/80 backdrop-blur-sm"
                  onClick={(e) => handleDeleteTable(table, e)}
                >
                  <Trash2 className="h-4 w-4" />
                </Button>
              )}

              <CardContent className="p-3 sm:p-4 text-center min-h-[120px] sm:min-h-[130px] flex flex-col justify-center relative z-10">
                <div className="mb-3">
                  <h3 className="text-sm sm:text-base font-bold mb-1 uppercase tracking-wide opacity-90">Table</h3>
                  <h2 className="text-2xl sm:text-3xl font-bold">{table.id}</h2>
                </div>

                <div className="flex items-center justify-center gap-2 mb-3 bg-white/20 rounded-full px-3 py-1 w-fit mx-auto">
                  <Users className="h-4 w-4 sm:h-5 sm:w-5" />
                  <span className="text-sm font-semibold">{table.capacity} seats</span>
                </div>

                {table.orderNumber && (
                  <div className="bg-white/30 rounded-xl px-3 py-2 mb-2 backdrop-blur-sm shadow-sm">
                    <span className="text-xs font-bold uppercase tracking-wide">KOT</span>
                    <div className="text-sm font-bold">#{table.orderNumber}</div>
                  </div>
                )}

                {table.orderTime && (
                  <div className="text-xs font-semibold opacity-90 mb-2 bg-white/20 rounded-full px-3 py-1 w-fit mx-auto">
                    {table.orderTime}
                  </div>
                )}

                <div className="mt-2">
                  <span className="text-sm font-bold px-4 py-2 rounded-full bg-white/30 backdrop-blur-sm shadow-sm">
                    {getStatusText(table.status)}
                  </span>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>

      {/* Add New Table Dialog */}
      <Dialog open={isAddTableDialogOpen} onOpenChange={setIsAddTableDialogOpen}>
        <DialogContent
          className="max-w-md"
          style={{zIndex: 9999}}
          onKeyDown={(e) => {
            if (e.key === 'Escape') {
              setIsAddTableDialogOpen(false);
            }
          }}
        >
          <DialogHeader>
            <DialogTitle>Add New Table</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label htmlFor="tableNumber">Table Number (Optional)</Label>
              <Input
                id="tableNumber"
                type="text"
                value={newTableNumber}
                onChange={(e) => setNewTableNumber(e.target.value)}
                placeholder="Leave empty for auto-number"
                className="mt-1"
                autoFocus
                onKeyDown={(e) => {
                  if (e.key === 'Enter') {
                    e.preventDefault();
                    // Move focus to capacity input
                    const capacityInput = document.getElementById('capacity');
                    if (capacityInput) {
                      capacityInput.focus();
                    }
                  } else if (e.key === 'ArrowDown') {
                    e.preventDefault();
                    const capacityInput = document.getElementById('capacity');
                    if (capacityInput) {
                      capacityInput.focus();
                    }
                  }
                }}
              />
              <p className="text-sm text-gray-500 mt-1">
                Specify table number or leave empty for auto-numbering
              </p>
            </div>
            <div>
              <Label htmlFor="capacity">Table Capacity (Number of Seats)</Label>
              <Input
                id="capacity"
                type="number"
                min="1"
                max="20"
                value={newTableCapacity}
                onChange={(e) => setNewTableCapacity(e.target.value)}
                placeholder="Enter number of seats"
                className="mt-1"
                onKeyDown={(e) => {
                  if (e.key === 'Enter') {
                    e.preventDefault();
                    handleAddTable();
                  } else if (e.key === 'ArrowUp') {
                    e.preventDefault();
                    const tableNumberInput = document.getElementById('tableNumber');
                    if (tableNumberInput) {
                      tableNumberInput.focus();
                    }
                  }
                }}
              />
              <p className="text-sm text-gray-500 mt-1">
                Enter the number of seats for this table (1-20)
              </p>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsAddTableDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleAddTable}>
              Add Table
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent style={{zIndex: 10000}}>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure you want to delete this table?</AlertDialogTitle>
            <AlertDialogDescription>
              {tableToDelete && (
                <>
                  You are about to delete <strong>Table {tableToDelete.id}</strong> with {tableToDelete.capacity} seats.
                  This action cannot be undone.
                </>
              )}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={() => setIsDeleteDialogOpen(false)}>
              No, Cancel
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={confirmDeleteTable}
              className="bg-red-600 hover:bg-red-700 text-white"
            >
              Yes, Delete Table
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
};

export default AdminTableManagement;
