// Unified Print Service
// Handles both thermal printer and browser printing

import { KOT } from '../utils/kotStorage';
import { Customer } from '../utils/customerStorage';
import { getGlobalPrinterService, isThermalPrinterAvailable } from './thermalPrinter';
import { getGlobalMobilePrinter, isMobilePrinterAvailable } from './mobileThermalPrinter';
import { thermalFormatter } from '../utils/thermalPrintFormatter';
import { gstSettingsManager } from '../utils/gstSettings';
import { restaurantSettingsManager } from '../utils/restaurantSettings';
import { CustomerOrder } from '../utils/customerOrdersStorage';

export interface PrintOptions {
  includeGST?: boolean;
  discount?: number;
  discountType?: 'percentage' | 'fixed';
  paymentMethod?: string;
  customerNotes?: string;
}

export class PrintService {
  // Print KOT (Kitchen Order Ticket) with mobile support
  static async printKOT(kot: KOT): Promise<boolean> {
    try {
      // Always try mobile thermal printer first (Bluetooth pairing)
      const mobilePrinter = getGlobalMobilePrinter();
      if (isMobilePrinterAvailable() && mobilePrinter) {
        console.log('Attempting mobile thermal print (Bluetooth)...');
        const success = await mobilePrinter.printKOT(kot);
        if (success) {
          console.log('KOT printed via mobile thermal printer (Bluetooth)');
          return true;
        } else {
          console.warn('Mobile thermal print failed, falling back to browser print');
        }
      }

      // Fallback to browser printing only if mobile thermal fails
      console.log('Using browser print fallback');
      return this.browserPrintKOT(kot);
    } catch (error) {
      console.error('Print KOT error:', error);
      return this.browserPrintKOT(kot);
    }
  }

  // Print Bill
  static async printBill(
    kot: KOT,
    customer: Customer | null,
    options: PrintOptions = {}
  ): Promise<boolean> {
    try {
      const {
        includeGST = true,
        discount = 0,
        discountType = 'percentage',
        paymentMethod = 'cash'
      } = options;

      // Always try mobile thermal printer first (Bluetooth pairing)
      const mobilePrinter = getGlobalMobilePrinter();
      if (isMobilePrinterAvailable() && mobilePrinter) {
        console.log('Attempting mobile thermal bill print (Bluetooth)...');
        const billData = {
          kot,
          customer,
          includeGST,
          discount,
          discountType,
          paymentMethod
        };
        const success = await mobilePrinter.printBill(billData);
        if (success) {
          console.log('Bill printed via mobile thermal printer (Bluetooth)');
          return true;
        } else {
          console.warn('Mobile thermal bill print failed, falling back to browser print');
        }
      }

      // Fallback to browser printing only if mobile thermal fails
      return this.browserPrintBill(kot, customer, options);
    } catch (error) {
      console.error('Print bill error:', error);
      return this.browserPrintBill(kot, customer, options);
    }
  }

  // Browser print KOT (existing functionality)
  private static browserPrintKOT(kot: KOT): boolean {
    try {
      const kotContent = this.generateKOTContent(kot);
      const printWindow = window.open('', '_blank');
      
      if (printWindow) {
        printWindow.document.write(kotContent);
        printWindow.document.close();
        printWindow.print();
        return true;
      }
      return false;
    } catch (error) {
      console.error('Browser print KOT failed:', error);
      return false;
    }
  }

  // Browser print bill (existing functionality)
  private static browserPrintBill(
    kot: KOT, 
    customer: Customer | null, 
    options: PrintOptions = {}
  ): boolean {
    try {
      const billContent = this.generateBillContent(kot, customer, options);
      const printWindow = window.open('', '_blank');
      
      if (printWindow) {
        printWindow.document.write(billContent);
        printWindow.document.close();
        printWindow.print();
        return true;
      }
      return false;
    } catch (error) {
      console.error('Browser print bill failed:', error);
      return false;
    }
  }

  // Generate KOT content for browser printing
  private static generateKOTContent(kot: KOT): string {
    const currentDate = new Date().toLocaleDateString('en-IN');
    const currentTime = new Date().toLocaleTimeString('en-IN');

    return `
    <!DOCTYPE html>
    <html>
    <head>
      <title>KOT ${kot.kotNumber}</title>
      <style>
        body { 
          font-family: 'Courier New', monospace; 
          font-size: 14px; 
          margin: 20px; 
          line-height: 1.4;
        }
        .header { text-align: center; margin-bottom: 20px; }
        .kot-details { margin-bottom: 20px; }
        .items { margin-bottom: 20px; }
        .item { margin-bottom: 10px; padding: 5px 0; border-bottom: 1px dashed #ccc; }
        .footer { text-align: center; margin-top: 20px; }
        @media print { 
          body { margin: 0; font-size: 12px; } 
          .no-print { display: none; }
        }
      </style>
    </head>
    <body>
      <div class="header">
        <h2>WOK KA TADKA</h2>
        <h3>KITCHEN ORDER TICKET</h3>
      </div>
      
      <div class="kot-details">
        <strong>KOT Number:</strong> ${kot.kotNumber}<br>
        <strong>Table:</strong> ${kot.tableId}<br>
        <strong>Date:</strong> ${currentDate}<br>
        <strong>Time:</strong> ${currentTime}
      </div>
      
      <div class="items">
        <h4>ITEMS:</h4>
        ${kot.items.map(item => `
          <div class="item">
            <strong>${item.name}</strong><br>
            Quantity: ${item.quantity} | Price: ₹${item.price.toFixed(2)}
            ${item.specialInstructions ? `<br><em>Note: ${item.specialInstructions}</em>` : ''}
          </div>
        `).join('')}
      </div>
      
      ${kot.specialInstructions ? `
        <div class="special-instructions">
          <strong>Special Instructions:</strong><br>
          ${kot.specialInstructions}
        </div>
      ` : ''}
      
      <div class="footer">
        <p><strong>KITCHEN COPY</strong></p>
      </div>
    </body>
    </html>`;
  }

  // Generate bill content for browser printing
  private static generateBillContent(
    kot: KOT, 
    customer: Customer | null, 
    options: PrintOptions = {}
  ): string {
    const {
      includeGST = true,
      discount = 0,
      discountType = 'percentage',
      paymentMethod = 'cash',
      customerNotes = ''
    } = options;

    const currentDate = new Date().toLocaleDateString('en-IN');
    const currentTime = new Date().toLocaleTimeString('en-IN');

    // Get GST settings and restaurant information
    const gstSettings = gstSettingsManager.getGSTSettings();
    const restaurantInfo = restaurantSettingsManager.getFormattedHeader();

    // Calculate totals
    let subtotal = kot.items.reduce((sum, item) => sum + (item.price * item.quantity), 0);

    const discountAmount = discountType === 'percentage'
      ? (subtotal * discount) / 100
      : discount;

    subtotal -= discountAmount;

    // Calculate GST using proper settings
    const cgstAmount = includeGST ? (subtotal * gstSettings.cgstRate) / 100 : 0;
    const sgstAmount = includeGST ? (subtotal * gstSettings.sgstRate) / 100 : 0;
    const gstAmount = cgstAmount + sgstAmount;
    const grandTotal = subtotal + gstAmount;

    return `
    <!DOCTYPE html>
    <html>
    <head>
      <title>Bill ${kot.kotNumber}</title>
      <style>
        body { 
          font-family: Arial, sans-serif; 
          font-size: 14px; 
          margin: 20px; 
          line-height: 1.4;
        }
        .header { text-align: center; margin-bottom: 20px; }
        .bill-details { margin-bottom: 20px; }
        .customer-details { margin-bottom: 20px; padding: 10px; background: #f5f5f5; }
        .items-table { width: 100%; border-collapse: collapse; margin-bottom: 20px; }
        .items-table th, .items-table td { 
          border: 1px solid #ddd; 
          padding: 8px; 
          text-align: left; 
        }
        .items-table th { background-color: #f2f2f2; }
        .totals { margin-bottom: 20px; }
        .total-row { display: flex; justify-content: space-between; margin: 5px 0; }
        .grand-total { font-weight: bold; font-size: 16px; }
        .footer { text-align: center; margin-top: 20px; }
        @media print { 
          body { margin: 0; font-size: 12px; } 
        }
      </style>
    </head>
    <body>
      <div class="header">
        <h2>${restaurantInfo.name}</h2>
        <p>${restaurantInfo.address}</p>
        <p>Phone: ${restaurantInfo.phone}</p>
        <h3>RESTAURANT BILL</h3>
        <p>GST: ${restaurantSettingsManager.getGSTNumber()}</p>
      </div>
      
      <div class="bill-details">
        <strong>Bill Number:</strong> ${kot.kotNumber}<br>
        <strong>Table:</strong> ${kot.tableId}<br>
        <strong>Date:</strong> ${currentDate}<br>
        <strong>Time:</strong> ${currentTime}
      </div>
      
      ${customer ? `
        <div class="customer-details">
          <strong>Customer Details:</strong><br>
          <strong>Name:</strong> ${customer.name}<br>
          ${customer.phone ? `<strong>Phone:</strong> ${customer.phone}<br>` : ''}
          ${customer.email ? `<strong>Email:</strong> ${customer.email}<br>` : ''}
          ${customer.isRegular ? '<strong>Status:</strong> Regular Customer ⭐<br>' : ''}
        </div>
      ` : ''}
      
      <table class="items-table">
        <thead>
          <tr>
            <th>Item</th>
            <th>Qty</th>
            <th>Price</th>
            <th>Total</th>
          </tr>
        </thead>
        <tbody>
          ${kot.items.map(item => `
            <tr>
              <td>${item.name}</td>
              <td>${item.quantity}</td>
              <td>₹${item.price.toFixed(2)}</td>
              <td>₹${(item.price * item.quantity).toFixed(2)}</td>
            </tr>
          `).join('')}
        </tbody>
      </table>
      
      <div class="totals">
        <div class="total-row">
          <span>Subtotal:</span>
          <span>₹${(subtotal + discountAmount).toFixed(2)}</span>
        </div>
        ${discount > 0 ? `
          <div class="total-row">
            <span>Discount (${discountType === 'percentage' ? discount + '%' : 'Fixed'}):</span>
            <span>-₹${discountAmount.toFixed(2)}</span>
          </div>
        ` : ''}
        ${includeGST ? `
          <div class="total-row">
            <span>CGST (${gstSettings.cgstRate}%):</span>
            <span>₹${cgstAmount.toFixed(2)}</span>
          </div>
          <div class="total-row">
            <span>SGST (${gstSettings.sgstRate}%):</span>
            <span>₹${sgstAmount.toFixed(2)}</span>
          </div>
        ` : ''}
        <div class="total-row grand-total">
          <span>Grand Total ${includeGST ? '(incl. GST)' : '(excl. GST)'}:</span>
          <span>₹${grandTotal.toFixed(2)}</span>
        </div>
      </div>
      
      <div class="payment-info">
        <strong>Payment Method:</strong> ${paymentMethod.toUpperCase()}
      </div>
      
      ${customerNotes ? `
        <div class="notes">
          <strong>Customer Notes:</strong><br>
          ${customerNotes}
        </div>
      ` : ''}
      
      <div class="footer">
        <p>Thank you for dining with us!</p>
        <p>Visit us again soon!</p>
        <p>Generated on ${currentDate} at ${currentTime}</p>
      </div>
    </body>
    </html>`;
  }

  // Get print status
  static getPrintStatus(): { thermalAvailable: boolean; queueLength: number } {
    const printerService = getGlobalPrinterService();
    
    if (printerService) {
      const status = printerService.getStatus();
      return {
        thermalAvailable: status.connected,
        queueLength: status.queueLength
      };
    }
    
    return {
      thermalAvailable: false,
      queueLength: 0
    };
  }

  // Print Customer Order KOT
  static async printCustomerOrderKOT(order: CustomerOrder): Promise<boolean> {
    try {
      // Always try mobile thermal printer first (Bluetooth pairing)
      const mobilePrinter = getGlobalMobilePrinter();
      if (isMobilePrinterAvailable() && mobilePrinter) {
        console.log('Attempting mobile thermal print for customer order KOT (Bluetooth)...');
        const thermalContent = thermalFormatter.formatCustomerOrderKOT(order);
        const success = await mobilePrinter.printContent(thermalContent, 'kot');
        if (success) {
          console.log('Customer Order KOT printed via mobile thermal printer (Bluetooth)');
          return true;
        } else {
          console.warn('Mobile thermal print failed, falling back to browser print');
        }
      }

      // Fallback to browser printing
      console.log('Using browser print fallback for customer order KOT');
      return this.browserPrintCustomerOrderKOT(order);
    } catch (error) {
      console.error('Print Customer Order KOT error:', error);
      return this.browserPrintCustomerOrderKOT(order);
    }
  }

  // Print Customer Order Receipt
  static async printCustomerOrderReceipt(order: CustomerOrder): Promise<boolean> {
    try {
      // Always try mobile thermal printer first (Bluetooth pairing)
      const mobilePrinter = getGlobalMobilePrinter();
      if (isMobilePrinterAvailable() && mobilePrinter) {
        console.log('Attempting mobile thermal print for customer order receipt (Bluetooth)...');
        const thermalContent = thermalFormatter.formatCustomerOrderReceipt(order);
        const success = await mobilePrinter.printContent(thermalContent, 'bill');
        if (success) {
          console.log('Customer Order Receipt printed via mobile thermal printer (Bluetooth)');
          return true;
        } else {
          console.warn('Mobile thermal print failed, falling back to browser print');
        }
      }

      // Fallback to browser printing
      console.log('Using browser print fallback for customer order receipt');
      return this.browserPrintCustomerOrderReceipt(order);
    } catch (error) {
      console.error('Print Customer Order Receipt error:', error);
      return this.browserPrintCustomerOrderReceipt(order);
    }
  }

  // Browser fallback for Customer Order KOT printing
  private static browserPrintCustomerOrderKOT(order: CustomerOrder): Promise<boolean> {
    return new Promise((resolve) => {
      try {
        const printWindow = window.open('', '_blank');
        if (!printWindow) {
          console.error('Failed to open print window');
          resolve(false);
          return;
        }

        const thermalContent = thermalFormatter.formatCustomerOrderKOT(order);
        const htmlContent = this.generateCustomerOrderKOTHTML(order, thermalContent);

        printWindow.document.write(htmlContent);
        printWindow.document.close();

        printWindow.onload = () => {
          printWindow.print();
          printWindow.close();
          resolve(true);
        };

        // Fallback timeout
        setTimeout(() => {
          if (!printWindow.closed) {
            printWindow.close();
          }
          resolve(true);
        }, 3000);
      } catch (error) {
        console.error('Browser print error:', error);
        resolve(false);
      }
    });
  }

  // Browser fallback for Customer Order Receipt printing
  private static browserPrintCustomerOrderReceipt(order: CustomerOrder): Promise<boolean> {
    return new Promise((resolve) => {
      try {
        const printWindow = window.open('', '_blank');
        if (!printWindow) {
          console.error('Failed to open print window');
          resolve(false);
          return;
        }

        const thermalContent = thermalFormatter.formatCustomerOrderReceipt(order);
        const htmlContent = this.generateCustomerOrderReceiptHTML(order, thermalContent);

        printWindow.document.write(htmlContent);
        printWindow.document.close();

        printWindow.onload = () => {
          printWindow.print();
          printWindow.close();
          resolve(true);
        };

        // Fallback timeout
        setTimeout(() => {
          if (!printWindow.closed) {
            printWindow.close();
          }
          resolve(true);
        }, 3000);
      } catch (error) {
        console.error('Browser print error:', error);
        resolve(false);
      }
    });
  }

  // Generate HTML for Customer Order KOT printing
  private static generateCustomerOrderKOTHTML(order: CustomerOrder, thermalContent: string): string {
    const currentDate = new Date().toLocaleDateString();
    const currentTime = new Date().toLocaleTimeString();

    return `<!DOCTYPE html>
    <html>
    <head>
      <title>Customer Order KOT - ${order.orderNumber}</title>
      <style>
        @media print {
          body { margin: 0; }
          .no-print { display: none; }
        }
        body {
          font-family: 'Courier New', monospace;
          font-size: 12px;
          line-height: 1.2;
          margin: 20px;
          white-space: pre-wrap;
        }
        .thermal-content {
          border: 1px solid #ccc;
          padding: 10px;
          background: white;
          max-width: 300px;
        }
        .header {
          text-align: center;
          font-weight: bold;
          margin-bottom: 10px;
        }
        .footer {
          text-align: center;
          margin-top: 20px;
          font-size: 10px;
          color: #666;
        }
      </style>
    </head>
    <body>
      <div class="header">
        <h2>Customer Order KOT</h2>
        <p>Order: ${order.orderNumber} | Type: ${order.type.toUpperCase()}</p>
      </div>

      <div class="thermal-content">${thermalContent}</div>

      <div class="footer">
        <p>Generated on ${currentDate} at ${currentTime}</p>
      </div>
    </body>
    </html>`;
  }

  // Generate HTML for Customer Order Receipt printing
  private static generateCustomerOrderReceiptHTML(order: CustomerOrder, thermalContent: string): string {
    const currentDate = new Date().toLocaleDateString();
    const currentTime = new Date().toLocaleTimeString();

    return `<!DOCTYPE html>
    <html>
    <head>
      <title>Customer Order Receipt - ${order.orderNumber}</title>
      <style>
        @media print {
          body { margin: 0; }
          .no-print { display: none; }
        }
        body {
          font-family: 'Courier New', monospace;
          font-size: 12px;
          line-height: 1.2;
          margin: 20px;
          white-space: pre-wrap;
        }
        .thermal-content {
          border: 1px solid #ccc;
          padding: 10px;
          background: white;
          max-width: 300px;
        }
        .header {
          text-align: center;
          font-weight: bold;
          margin-bottom: 10px;
        }
        .footer {
          text-align: center;
          margin-top: 20px;
          font-size: 10px;
          color: #666;
        }
      </style>
    </head>
    <body>
      <div class="header">
        <h2>Customer Order Receipt</h2>
        <p>Order: ${order.orderNumber} | Type: ${order.type.toUpperCase()}</p>
        <p>Total: ₹${order.pricing.total.toFixed(2)}</p>
      </div>

      <div class="thermal-content">${thermalContent}</div>

      <div class="footer">
        <p>Generated on ${currentDate} at ${currentTime}</p>
      </div>
    </body>
    </html>`;
  }
}

export default PrintService;
