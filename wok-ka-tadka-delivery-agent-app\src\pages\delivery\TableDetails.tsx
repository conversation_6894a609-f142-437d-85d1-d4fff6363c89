import { useState, useEffect } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Switch } from "@/components/ui/switch";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { gstSettingsManager, GSTSettings } from "@/utils/gstSettings";
import {
  ArrowLeft,
  Users,
  Clock,
  DollarSign,
  Plus,
  CheckCircle,
  Receipt,
  Utensils,
  FileText,
  X,
  Printer,
  Calculator
} from "lucide-react";
import { useNavigate, useParams } from "react-router-dom";
import { Logo } from "@/components/ui/logo";
import { kotStorage } from "@/utils/kotStorage";
import { useToast } from "@/hooks/use-toast";
import CustomerSelectionDialog from "@/components/CustomerSelectionDialog";
import { Customer, customerManager } from "@/utils/customerStorage";
import PrintService from "@/services/printService";
import PaymentMethodDialog, { PaymentMethod } from "@/components/PaymentMethodDialog";

// Persistent GST setting utility (shared with admin)
const GST_PREFERENCE_KEY = 'admin_gst_preference';

const getGSTPreference = (): boolean => {
  const saved = localStorage.getItem(GST_PREFERENCE_KEY);
  return saved !== null ? JSON.parse(saved) : true; // Default to true
};

const setGSTPreference = (includeGST: boolean): void => {
  localStorage.setItem(GST_PREFERENCE_KEY, JSON.stringify(includeGST));
};

const TableDetails = () => {
  const navigate = useNavigate();
  const { tableId } = useParams();
  const { toast } = useToast();

  const [tableData, setTableData] = useState<any>(null);
  const [showKOTModal, setShowKOTModal] = useState(false);
  const [showCustomerDialog, setShowCustomerDialog] = useState(false);
  const [showPaymentMethodDialog, setShowPaymentMethodDialog] = useState(false);
  const [isPrintingBill, setIsPrintingBill] = useState(false);
  const [includeGST, setIncludeGST] = useState(getGSTPreference()); // GST toggle state with persistence
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState<PaymentMethod>('cash');
  const [gstSettings, setGSTSettings] = useState<GSTSettings>(
    gstSettingsManager.getGSTSettings()
  );

  useEffect(() => {
    if (tableId) {
      const activeKOT = kotStorage.getActiveKOTForTable(tableId);
      if (activeKOT && activeKOT.items && Array.isArray(activeKOT.items)) {
        setTableData({
          id: tableId,
          capacity: 4, // Default capacity
          guests: activeKOT.items.length,
          status: "occupied",
          orderTime: new Date(activeKOT.createdAt).toLocaleTimeString('en-IN', {
            hour: '2-digit',
            minute: '2-digit',
            hour12: true
          }),
          kotNumber: activeKOT.kotNumber,
          orders: activeKOT.items.map((item: any, index: number) => ({
            id: index + 1,
            name: item.name || 'Unknown Item',
            quantity: item.quantity || 1,
            price: item.price || 0,
            status: "served" // Default status
          })),
          totalAmount: activeKOT.totalAmount || 0,
          createdAt: activeKOT.createdAt,
          versions: activeKOT.versions || []
        });
      } else {
        // No active KOT found, redirect back
        navigate("/delivery/tables");
      }
    }
  }, [tableId, navigate]);

  // Listen for GST settings changes
  useEffect(() => {
    const handleGSTSettingsChange = (event: CustomEvent<GSTSettings>) => {
      setGSTSettings(event.detail);
    };

    window.addEventListener('gstSettingsChanged', handleGSTSettingsChange as EventListener);

    return () => {
      window.removeEventListener('gstSettingsChanged', handleGSTSettingsChange as EventListener);
    };
  }, []);

  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // Only handle shortcuts when not in input fields
      if (event.target instanceof HTMLInputElement || event.target instanceof HTMLTextAreaElement) {
        return;
      }

      if (event.ctrlKey) {
        switch (event.key.toLowerCase()) {
          case 'a':
            event.preventDefault();
            // Add More Items
            navigate(`/delivery/table-order/${tableId}`);
            break;
          case 'k':
            event.preventDefault();
            // Print KOT
            handlePrintKOT();
            break;
          case 'p':
            event.preventDefault();
            // Print Bill
            if (!isPrintingBill) {
              handlePrintBill();
            }
            break;
          case 'c':
            event.preventDefault();
            // Complete Order
            handleCompleteOrder();
            break;
          case 'g':
            event.preventDefault();
            // Toggle GST
            handleGSTToggle(!includeGST);
            break;
        }
      }
    };

    document.addEventListener('keydown', handleKeyDown);

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [tableId, isPrintingBill, includeGST, navigate]);

  const getTotalAmount = () => {
    if (!tableData || !tableData.orders || !Array.isArray(tableData.orders)) return 0;
    return tableData.orders.reduce((total: number, order: any) => total + ((order.price || 0) * (order.quantity || 0)), 0);
  };

  const handleCompleteOrder = () => {
    if (tableData && tableData.kotNumber) {
      kotStorage.completeKOT(tableData.kotNumber);
      toast({
        title: "Order Completed!",
        description: `Table ${tableId} order has been completed successfully`,
      });
      navigate("/delivery/tables");
    }
  };

  // Handle GST toggle with persistence
  const handleGSTToggle = (checked: boolean) => {
    setIncludeGST(checked);
    setGSTPreference(checked);
    toast({
      title: `GST ${checked ? 'Enabled' : 'Disabled'}`,
      description: `GST will be ${checked ? 'included' : 'excluded'} in all future bills until changed`,
    });
  };

  const handlePrintKOT = async () => {
    if (!tableData || !tableData.kotNumber) {
      toast({
        title: "No KOT Found",
        description: "No active KOT found for this table to print.",
        variant: "destructive"
      });
      return;
    }

    try {
      // Get the active KOT from storage
      const activeKOT = kotStorage.getKOTByNumber(tableData.kotNumber);

      if (!activeKOT) {
        toast({
          title: "KOT Not Found",
          description: "The KOT for this table could not be found.",
          variant: "destructive"
        });
        return;
      }

      // Print the KOT using the print service
      const success = await PrintService.printKOT(activeKOT);

      if (success) {
        toast({
          title: "KOT Printed Successfully!",
          description: `KOT #${activeKOT.kotNumber} has been printed for Table ${tableId}.`,
        });
      } else {
        throw new Error("Print service failed");
      }
    } catch (error) {
      console.error('Failed to print KOT:', error);
      toast({
        title: "Print Error",
        description: "Failed to print KOT. Please try again.",
        variant: "destructive"
      });
    }
  };

  const generateBillContent = (kot: any, customer: Customer | null, includeGST: boolean = true): string => {
    const currentDate = new Date().toLocaleDateString('en-IN');
    const currentTime = new Date().toLocaleTimeString('en-IN');

    const subtotal = kot.totalAmount || 0;
    const gstAmount = includeGST ? (subtotal * gstSettings.totalGSTRate) / 100 : 0;
    const total = subtotal + gstAmount;

    return `
      <html>
        <head>
          <title>Bill - Table ${tableId}</title>
          <style>
            body { font-family: Arial, sans-serif; margin: 20px; }
            .header { text-align: center; margin-bottom: 20px; }
            .bill-details { margin: 20px 0; }
            .items-table { width: 100%; border-collapse: collapse; margin: 20px 0; }
            .items-table th, .items-table td { border: 1px solid #ddd; padding: 8px; text-align: left; }
            .items-table th { background-color: #f2f2f2; }
            .total-section { margin-top: 20px; font-weight: bold; }
            .footer { margin-top: 30px; text-align: center; font-size: 12px; }
          </style>
        </head>
        <body>
          <div class="header">
            <h2>Wok Ka Tadka</h2>
            <p>Mumbai Style Chinese & Indian</p>
            <p>Bill Receipt</p>
          </div>

          <div class="bill-details">
            <p><strong>Date:</strong> ${currentDate}</p>
            <p><strong>Time:</strong> ${currentTime}</p>
            <p><strong>Table:</strong> ${tableId}</p>
            <p><strong>KOT #:</strong> ${kot.kotNumber}</p>
            ${customer ? `<p><strong>Customer:</strong> ${customer.name}</p>` : ''}
            ${customer?.phone ? `<p><strong>Phone:</strong> ${customer.phone}</p>` : ''}
          </div>

          <table class="items-table">
            <thead>
              <tr>
                <th>Item</th>
                <th>Qty</th>
                <th>Price</th>
                <th>Amount</th>
              </tr>
            </thead>
            <tbody>
              ${kot.orders?.map((item: any) => `
                <tr>
                  <td>${item.name}</td>
                  <td>${item.quantity}</td>
                  <td>₹${item.price}</td>
                  <td>₹${item.price * item.quantity}</td>
                </tr>
              `).join('') || ''}
            </tbody>
          </table>

          <div class="total-section">
            <p>Subtotal: ₹${subtotal}</p>
            ${includeGST ? `
              <p>CGST (${gstSettings.cgstRate}%): ₹${((subtotal * gstSettings.cgstRate) / 100).toFixed(2)}</p>
              <p>SGST (${gstSettings.sgstRate}%): ₹${((subtotal * gstSettings.sgstRate) / 100).toFixed(2)}</p>
              <p>Total GST: ₹${gstAmount.toFixed(2)}</p>
            ` : '<p>GST: Not Applicable</p>'}
            <p style="font-size: 18px; border-top: 2px solid #000; padding-top: 10px;">
              <strong>Grand Total: ₹${total.toFixed(2)}</strong>
            </p>
          </div>

          <div class="footer">
            <p>Thank you for dining with us!</p>
            <p>Visit us again soon</p>
          </div>
        </body>
      </html>
    `;
  };

  const handlePrintBill = () => {
    setShowCustomerDialog(true);
  };

  const handleCustomerSelection = (customer: Customer | null) => {
    setShowCustomerDialog(false);
    setShowPaymentMethodDialog(true);
    // Store customer for later use
    setSelectedCustomer(customer);
  };

  const handlePaymentMethodSelection = (paymentMethod: PaymentMethod) => {
    setSelectedPaymentMethod(paymentMethod);
    setShowPaymentMethodDialog(false);
    printBill(selectedCustomer, paymentMethod);
  };

  const [selectedCustomer, setSelectedCustomer] = useState<Customer | null>(null);

  const printBill = async (customer: Customer | null, paymentMethod: PaymentMethod = 'cash') => {
    if (!tableId || !tableData) return;

    setIsPrintingBill(true);

    try {
      // Get current KOT for the table
      const activeKOT = kotStorage.getActiveKOTForTable(tableId);

      if (!activeKOT) {
        toast({
          title: "No Active KOT",
          description: "No active KOT found for this table to generate bill.",
          variant: "destructive"
        });
        return;
      }

      // Add customer order to records if customer is selected
      if (customer) {
        customerManager.addCustomerOrder({
          customerId: customer.id,
          orderId: activeKOT.kotNumber,
          tableId: tableId,
          kotNumber: activeKOT.kotNumber,
          amount: activeKOT.totalAmount,
          items: activeKOT.items.map(item => ({
            name: item.name,
            quantity: item.quantity,
            price: item.price
          })),
          orderDate: new Date().toISOString()
        });
      }

      // Print bill using unified print service
      const success = await PrintService.printBill(activeKOT, customer, {
        includeGST,
        paymentMethod
      });

      if (success) {
        toast({
          title: "Bill Printed Successfully!",
          description: customer
            ? `Bill printed for ${customer.name} - Table ${tableId}`
            : `Bill printed for Table ${tableId}`,
        });
      } else {
        toast({
          title: "Print Failed",
          description: "Bill printing failed. Please try again.",
          variant: "destructive"
        });
      }
    } catch (error) {
      toast({
        title: "Print Error",
        description: "Failed to print bill. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsPrintingBill(false);
    }
  };







  const getStatusColor = (status: string) => {
    switch (status) {
      case "served": return "bg-green-100 text-green-700";
      case "preparing": return "bg-yellow-100 text-yellow-700";
      case "pending": return "bg-red-100 text-red-700";
      default: return "bg-gray-100 text-gray-700";
    }
  };

  if (!tableData) {
    return (
      <div className="apk-page-container bg-gray-50">
        <div className="bg-gradient-primary text-white p-4 apk-header-fixed">
          <div className="flex items-center gap-3">
            <Button
              variant="ghost"
              size="icon"
              className="text-white hover:bg-white/20"
              onClick={() => navigate("/delivery/tables")}
            >
              <ArrowLeft className="h-5 w-5" />
            </Button>
            <div>
              <h1 className="text-lg font-semibold">Table {tableId}</h1>
              <p className="text-white/80 text-sm">Loading...</p>
            </div>
          </div>
        </div>
        <div className="p-4 apk-content-with-header">
          <div className="text-center">
            <p>Loading table details...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="apk-page-container bg-gray-50">
      {/* Header */}
      <div className="bg-gradient-primary text-white p-4 apk-header-fixed">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <Button
              variant="ghost"
              size="icon"
              className="text-white hover:bg-white/20 back-button-highlight tap-target"
              onClick={() => navigate("/delivery/tables")}
            >
              <ArrowLeft className="h-5 w-5" />
            </Button>
            <div>
              <h1 className="text-lg font-semibold">Table {tableId}</h1>
              <p className="text-white/80 text-sm">Order Details</p>
            </div>
          </div>
          <Logo size="sm" variant="white" />
        </div>
      </div>

      <div className="p-4 space-y-4 apk-content-with-header">
        {/* Table Info */}
        <Card className="shadow-card border-0 bg-white">
          <CardContent className="p-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="flex items-center gap-3">
                <div className="bg-blue-100 p-2 rounded-lg">
                  <Users className="h-5 w-5 text-blue-600" />
                </div>
                <div>
                  <p className="text-sm text-gray-600">Guests</p>
                  <p className="font-semibold">{tableData?.guests || 0}/{tableData?.capacity || 4}</p>
                </div>
              </div>
              <div className="flex items-center gap-3">
                <div className="bg-green-100 p-2 rounded-lg">
                  <Clock className="h-5 w-5 text-green-600" />
                </div>
                <div>
                  <p className="text-sm text-gray-600">Order Time</p>
                  <p className="font-semibold">{tableData?.orderTime || 'N/A'}</p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Current Orders */}
        <Card className="shadow-card border-0 bg-white">
          <CardHeader className="pb-3">
            <CardTitle className="text-lg flex items-center gap-2">
              <Utensils className="h-5 w-5" />
              Current Orders
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            {tableData?.orders && Array.isArray(tableData.orders) ? tableData.orders.map((order) => (
              <div key={order.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div className="flex-1">
                  <h4 className="font-medium text-gray-900">{order.name || 'Unknown Item'}</h4>
                  <p className="text-sm text-gray-600">Qty: {order.quantity || 0} × ₹{order.price || 0}</p>
                </div>
                <div className="text-right">
                  <Badge className={`${getStatusColor(order.status)} text-xs mb-1`}>
                    {order.status}
                  </Badge>
                  <p className="font-semibold text-primary">₹{(order.price || 0) * (order.quantity || 0)}</p>
                </div>
              </div>
            )) : (
              <div className="text-center py-8 text-gray-500">
                <p>No orders found for this table</p>
              </div>
            )}

            <div className="border-t pt-3 mt-4">
              <div className="flex justify-between items-center text-lg font-bold">
                <span>Total Amount:</span>
                <span className="text-primary">₹{getTotalAmount()}</span>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* GST Toggle */}
        <div className="bg-yellow-50 p-3 rounded-lg border border-yellow-200">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Calculator className="h-4 w-4 text-yellow-600" />
              <div>
                <span className="font-medium text-sm text-gray-900">GST (CGST + SGST)</span>
                <p className="text-xs text-gray-600">Include {gstSettings.totalGSTRate}% GST in bill • Press Ctrl+G to toggle</p>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <span className={`text-xs font-medium ${includeGST ? 'text-green-600' : 'text-gray-500'}`}>
                {includeGST ? 'With GST' : 'Without GST'}
              </span>
              <Switch
                checked={includeGST}
                onCheckedChange={handleGSTToggle}
                className="data-[state=checked]:bg-green-600"
              />
            </div>
          </div>
          {!includeGST && (
            <div className="mt-2 p-2 bg-orange-50 rounded border border-orange-200">
              <p className="text-xs text-orange-700">
                <strong>Note:</strong> GST will be excluded from this bill.
              </p>
            </div>
          )}
        </div>

        {/* Action Buttons */}
        <div className="space-y-3">
          <Button
            variant="delivery"
            size="lg"
            className="w-full h-12 sm:h-14 bg-orange-600 hover:bg-orange-700 text-sm sm:text-base"
            onClick={() => navigate(`/delivery/table-order/${tableId}`)}
            title="Ctrl+A"
          >
            <Plus className="h-4 w-4 mr-2" />
            Add More Items
            <span className="hidden sm:inline text-xs opacity-70 ml-2">(Ctrl+A)</span>
          </Button>

          <Button
            variant="outline"
            size="lg"
            className="w-full h-12 bg-green-500 hover:bg-green-600 text-white border-green-500"
            onClick={handlePrintKOT}
            title="Ctrl+K"
          >
            <Printer className="h-4 w-4 mr-2" />
            Print KOT
            <span className="hidden sm:inline text-xs opacity-70 ml-2">(Ctrl+K)</span>
          </Button>

          <Button
            variant="outline"
            size="lg"
            className="w-full h-12 bg-blue-500 hover:bg-blue-600 text-white border-blue-500"
            onClick={handlePrintBill}
            disabled={isPrintingBill}
            title="Ctrl+P"
          >
            {isPrintingBill ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                <span>Printing Bill...</span>
              </>
            ) : (
              <>
                <Printer className="h-4 w-4 mr-2" />
                <span>Print Bill</span>
                <span className="hidden sm:inline text-xs opacity-70 ml-2">(Ctrl+P)</span>
              </>
            )}
          </Button>

          <Button
            variant="outline"
            size="lg"
            className="w-full h-12 border-red-300 text-red-700 hover:bg-red-100"
            onClick={handleCompleteOrder}
            title="Ctrl+C"
          >
            <CheckCircle className="h-4 w-4 mr-2" />
            Complete Order
            <span className="hidden sm:inline text-xs opacity-70 ml-2">(Ctrl+C)</span>
          </Button>
        </div>

        {/* Order Timeline */}
        <Card className="shadow-card border-0 bg-white">
          <CardHeader className="pb-3">
            <CardTitle className="text-lg">Order Timeline</CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="flex items-center gap-3">
              <div className="w-3 h-3 bg-green-500 rounded-full"></div>
              <div className="flex-1">
                <p className="font-medium text-gray-900">Order Placed</p>
                <p className="text-sm text-gray-600">12:30 PM</p>
              </div>
            </div>
            <div className="flex items-center gap-3">
              <div className="w-3 h-3 bg-green-500 rounded-full"></div>
              <div className="flex-1">
                <p className="font-medium text-gray-900">KOT Sent to Kitchen</p>
                <p className="text-sm text-gray-600">12:32 PM</p>
              </div>
            </div>
            <div className="flex items-center gap-3">
              <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
              <div className="flex-1">
                <p className="font-medium text-gray-900">Food Being Prepared</p>
                <p className="text-sm text-gray-600">In Progress</p>
              </div>
            </div>
            <div className="flex items-center gap-3">
              <div className="w-3 h-3 bg-gray-300 rounded-full"></div>
              <div className="flex-1">
                <p className="font-medium text-gray-400">Ready to Serve</p>
                <p className="text-sm text-gray-400">Pending</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>



      {/* KOT Details Modal */}
      <Dialog open={showKOTModal} onOpenChange={setShowKOTModal}>
        <DialogContent className="w-[95vw] max-w-4xl mx-auto rounded-lg max-h-[90vh] flex flex-col apk-modal-above-header">
          <DialogHeader className="pb-3 sm:pb-4 flex-shrink-0">
            <div className="flex items-center justify-between">
              <DialogTitle className="text-base sm:text-lg font-semibold text-orange-600">
                Complete KOT Details - Table {tableId}
              </DialogTitle>
              <Button
                variant="ghost"
                size="icon"
                onClick={() => setShowKOTModal(false)}
                className="h-7 w-7 sm:h-8 sm:w-8 rounded-full"
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
          </DialogHeader>

          <div className="flex-1 overflow-y-auto space-y-4">
            {(() => {
              const activeKOT = kotStorage.getActiveKOTForTable(tableId);
              if (!activeKOT) return <p className="text-center py-8 text-gray-500">No KOT data available</p>;

              return (
                <div className="space-y-4">
                  {/* KOT Header Info */}
                  <div className="bg-orange-50 rounded-lg p-4 border border-orange-200">
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <span className="text-gray-600">KOT Number:</span>
                        <span className="font-semibold ml-2">{activeKOT?.kotNumber || 'N/A'}</span>
                      </div>
                      <div>
                        <span className="text-gray-600">Table:</span>
                        <span className="font-semibold ml-2">{tableId}</span>
                      </div>
                      <div>
                        <span className="text-gray-600">Created:</span>
                        <span className="font-semibold ml-2">
                          {activeKOT?.createdAt ? new Date(activeKOT.createdAt).toLocaleString('en-IN') : 'N/A'}
                        </span>
                      </div>
                      <div>
                        <span className="text-gray-600">Status:</span>
                        <Badge className="ml-2 bg-green-100 text-green-700">
                          {activeKOT?.status || 'Unknown'}
                        </Badge>
                      </div>
                    </div>
                  </div>

                  {/* Order History */}
                  <div className="space-y-4">
                    <h4 className="font-semibold text-gray-900 flex items-center gap-2">
                      <FileText className="h-4 w-4" />
                      Order History
                    </h4>

                    {activeKOT?.versions && Array.isArray(activeKOT.versions) && activeKOT.versions.length > 0 ? (
                      activeKOT.versions.map((version: any, versionIndex: number) => (
                        <div key={versionIndex} className="bg-white rounded-lg p-4 border border-gray-200">
                          <div className="flex items-center justify-between mb-3">
                            <h5 className="font-semibold text-gray-800 flex items-center gap-2">
                              <Clock className="h-4 w-4 text-orange-600" />
                              Order #{versionIndex + 1}
                              {versionIndex === 0 && <Badge className="bg-green-100 text-green-700 text-xs">Initial</Badge>}
                              {versionIndex > 0 && <Badge className="bg-orange-100 text-orange-700 text-xs">Added</Badge>}
                            </h5>
                            <span className="text-xs text-gray-500">
                              {version?.addedAt ? new Date(version.addedAt).toLocaleString('en-IN') : 'N/A'}
                            </span>
                          </div>

                          <div className="space-y-2">
                            {version?.addedItems && Array.isArray(version.addedItems) ? version.addedItems.map((item: any, itemIndex: number) => (
                              <div key={itemIndex} className="flex justify-between items-center py-2 border-b border-gray-100 last:border-b-0">
                                <div className="flex items-center gap-2">
                                  <span className="font-medium text-gray-900">{item?.name || 'Unknown Item'}</span>
                                  {item?.veg ? (
                                    <div className="w-3 h-3 border border-green-500 rounded-sm flex items-center justify-center">
                                      <div className="w-1.5 h-1.5 bg-green-500 rounded-full"></div>
                                    </div>
                                  ) : (
                                    <div className="w-3 h-3 border border-red-500 rounded-sm flex items-center justify-center">
                                      <div className="w-1.5 h-1.5 bg-red-500 rounded-full"></div>
                                    </div>
                                  )}
                                </div>
                                <div className="flex items-center gap-2 text-gray-700">
                                  <span className="text-sm">₹{item?.price || 0} × {item?.quantity || 0}</span>
                                  <span className="font-semibold">₹{(item?.price || 0) * (item?.quantity || 0)}</span>
                                </div>
                              </div>
                            )) : (
                              <div className="text-center py-4 text-gray-500">
                                <p>No items in this order</p>
                              </div>
                            )}
                          </div>

                          {version.specialInstructions && (
                            <div className="mt-3 p-2 bg-yellow-50 rounded text-sm text-yellow-800">
                              <strong>Instructions:</strong> {version.specialInstructions}
                            </div>
                          )}
                        </div>
                      ))
                    ) : (
                      <div className="bg-gray-50 rounded-lg p-4">
                        <p className="text-gray-600 text-sm">No order history available.</p>
                      </div>
                    )}
                  </div>

                  {/* Total Summary */}
                  <div className="bg-orange-50 rounded-lg p-4 border border-orange-200">
                    <div className="flex justify-between items-center text-lg font-bold text-orange-900">
                      <span>Grand Total ({activeKOT?.items?.length || 0} items):</span>
                      <span>₹{activeKOT?.totalAmount || 0}</span>
                    </div>
                  </div>
                </div>
              );
            })()}
          </div>
        </DialogContent>
      </Dialog>

      {/* Customer Selection Dialog */}
      <CustomerSelectionDialog
        isOpen={showCustomerDialog}
        onClose={() => setShowCustomerDialog(false)}
        onCustomerSelect={handleCustomerSelection}
        title="Select Customer for Bill"
        description="Choose a customer for this bill or skip to print without customer details"
        showEditButtons={false}
        showPhoneNumbers={false}
      />

      {/* Payment Method Selection Dialog */}
      <PaymentMethodDialog
        open={showPaymentMethodDialog}
        onClose={() => setShowPaymentMethodDialog(false)}
        onSelect={handlePaymentMethodSelection}
        title="Select Payment Method"
      />
    </div>
  );
};

export default TableDetails;
