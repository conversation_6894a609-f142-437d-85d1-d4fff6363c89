import { useState } from "react";
import { useEscapeKey } from "@/hooks/useEscapeKey";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  ArrowLeft,
  Download,
  RefreshCw,
  BarChart3,
  TrendingUp,
  TrendingDown,
  DollarSign,
  ShoppingCart,
  Users,
  Calendar,
  FileText,
  Printer,
  Package,
  Star
} from "lucide-react";
import { useNavigate } from "react-router-dom";
import { Logo } from "@/components/ui/logo";

const Reports = () => {
  const navigate = useNavigate();

  // Global Escape key handler for back navigation
  useEscapeKey('/admin/dashboard');

  const [selectedPeriod, setSelectedPeriod] = useState<"daily" | "weekly" | "monthly">("daily");
  const [startDate, setStartDate] = useState(new Date().toISOString().split('T')[0]);
  const [endDate, setEndDate] = useState(new Date().toISOString().split('T')[0]);

  // Mock report data
  const reportData = {
    daily: {
      sales: {
        totalRevenue: 15420,
        totalOrders: 47,
        avgOrderValue: 328,
        growth: 12
      },
      topItems: [
        { name: "Chicken Biryani", quantity: 15, revenue: 4500 },
        { name: "Paneer Butter Masala", quantity: 12, revenue: 3000 },
        { name: "Garlic Naan", quantity: 25, revenue: 1500 }
      ],
      hourlyBreakdown: [
        { hour: "9 AM", orders: 3, revenue: 850 },
        { hour: "12 PM", orders: 8, revenue: 2400 },
        { hour: "1 PM", orders: 12, revenue: 3200 },
        { hour: "7 PM", orders: 15, revenue: 4200 }
      ]
    },
    weekly: {
      sales: {
        totalRevenue: 98500,
        totalOrders: 312,
        avgOrderValue: 316,
        growth: 8
      },
      dailyBreakdown: [
        { day: "Monday", orders: 38, revenue: 12500 },
        { day: "Tuesday", orders: 45, revenue: 14200 },
        { day: "Wednesday", orders: 42, revenue: 13800 },
        { day: "Thursday", orders: 48, revenue: 15600 },
        { day: "Friday", orders: 52, revenue: 16800 },
        { day: "Saturday", orders: 58, revenue: 18200 },
        { day: "Sunday", orders: 29, revenue: 7400 }
      ]
    },
    monthly: {
      sales: {
        totalRevenue: 425000,
        totalOrders: 1340,
        avgOrderValue: 317,
        growth: 15
      },
      weeklyBreakdown: [
        { week: "Week 1", orders: 312, revenue: 98500 },
        { week: "Week 2", orders: 335, revenue: 105200 },
        { week: "Week 3", orders: 358, revenue: 112800 },
        { week: "Week 4", orders: 335, revenue: 108500 }
      ]
    }
  };

  const staffPerformance = [
    { name: "Sunita Devi", role: "Waiter", ordersServed: 156, rating: 4.8, tips: 12500 },
    { name: "Raj Kumar", role: "Waiter", ordersServed: 134, rating: 4.6, tips: 10800 },
    { name: "Amit Singh", role: "Delivery", deliveries: 89, rating: 4.9, earnings: 18500 }
  ];

  const inventoryReport = [
    { item: "Chicken Biryani", used: 45, remaining: 5, wastage: 2 },
    { item: "Paneer Butter Masala", used: 32, remaining: 3, wastage: 1 },
    { item: "Garlic Naan", used: 67, remaining: 12, wastage: 3 }
  ];

  const getCurrentData = () => reportData[selectedPeriod];

  const handleExportReport = (reportType: string) => {
    alert(`${reportType} report exported successfully!`);
  };

  const handlePrintReport = (reportType: string) => {
    alert(`${reportType} report sent to printer!`);
  };

  const currentData = getCurrentData();

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b sticky top-0 z-10">
        <div className="flex items-center justify-between p-4">
          <div className="flex items-center gap-4">
            <Button
              variant="ghost"
              size="icon"
              onClick={() => navigate("/admin/dashboard")}
              className="text-gray-600"
            >
              <ArrowLeft className="h-5 w-5" />
            </Button>
            <div>
              <h1 className="text-xl font-bold text-gray-900">Reports & Analytics</h1>
              <p className="text-sm text-gray-500">Business insights and performance reports</p>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => handleExportReport("All Reports")}
              className="flex items-center gap-2"
            >
              <Download className="h-4 w-4" />
              Export All
            </Button>
            <Button
              variant="ghost"
              size="icon"
              onClick={() => window.location.reload()}
              className="text-gray-600"
            >
              <RefreshCw className="h-5 w-5" />
            </Button>
          </div>
        </div>
      </div>

      <div className="p-6 space-y-6">
        {/* Period Selection */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Calendar className="h-5 w-5" />
              Report Period
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex flex-wrap gap-3 mb-4">
              <Button
                variant={selectedPeriod === "daily" ? "default" : "outline"}
                onClick={() => setSelectedPeriod("daily")}
              >
                Daily Report
              </Button>
              <Button
                variant={selectedPeriod === "weekly" ? "default" : "outline"}
                onClick={() => setSelectedPeriod("weekly")}
              >
                Weekly Report
              </Button>
              <Button
                variant={selectedPeriod === "monthly" ? "default" : "outline"}
                onClick={() => setSelectedPeriod("monthly")}
              >
                Monthly Report
              </Button>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="startDate">Start Date</Label>
                <Input
                  id="startDate"
                  type="date"
                  value={startDate}
                  onChange={(e) => setStartDate(e.target.value)}
                  className="mt-1"
                />
              </div>
              <div>
                <Label htmlFor="endDate">End Date</Label>
                <Input
                  id="endDate"
                  type="date"
                  value={endDate}
                  onChange={(e) => setEndDate(e.target.value)}
                  className="mt-1"
                />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Key Metrics */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <Card className="bg-gradient-to-r from-green-500 to-green-600 text-white">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-green-100 text-sm">Total Revenue</p>
                  <p className="text-2xl font-bold">₹{currentData.sales.totalRevenue.toLocaleString()}</p>
                  <div className="flex items-center gap-1 mt-1">
                    <TrendingUp className="h-4 w-4" />
                    <span className="text-sm">+{currentData.sales.growth}%</span>
                  </div>
                </div>
                <DollarSign className="h-8 w-8 text-green-200" />
              </div>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-r from-blue-500 to-blue-600 text-white">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-blue-100 text-sm">Total Orders</p>
                  <p className="text-2xl font-bold">{currentData.sales.totalOrders}</p>
                  <div className="flex items-center gap-1 mt-1">
                    <TrendingUp className="h-4 w-4" />
                    <span className="text-sm">+8%</span>
                  </div>
                </div>
                <ShoppingCart className="h-8 w-8 text-blue-200" />
              </div>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-r from-purple-500 to-purple-600 text-white">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-purple-100 text-sm">Avg Order Value</p>
                  <p className="text-2xl font-bold">₹{currentData.sales.avgOrderValue}</p>
                  <div className="flex items-center gap-1 mt-1">
                    <TrendingUp className="h-4 w-4" />
                    <span className="text-sm">+3%</span>
                  </div>
                </div>
                <BarChart3 className="h-8 w-8 text-purple-200" />
              </div>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-r from-orange-500 to-orange-600 text-white">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-orange-100 text-sm">Growth Rate</p>
                  <p className="text-2xl font-bold">{currentData.sales.growth}%</p>
                  <div className="flex items-center gap-1 mt-1">
                    <TrendingUp className="h-4 w-4" />
                    <span className="text-sm">vs last period</span>
                  </div>
                </div>
                <TrendingUp className="h-8 w-8 text-orange-200" />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Detailed Reports */}
        <Tabs defaultValue="sales" className="space-y-6">
          <TabsList className="grid grid-cols-2 w-full gap-2 bg-transparent p-0 relative z-10 mb-16">
            <TabsTrigger
              value="sales"
              className="text-xs font-medium px-3 py-2 h-10 rounded-md border-2 transition-all duration-200
                         data-[state=active]:bg-gradient-to-r data-[state=active]:from-blue-500 data-[state=active]:to-blue-600
                         data-[state=active]:text-white data-[state=active]:border-blue-600 data-[state=active]:shadow-lg
                         hover:bg-blue-50 hover:text-blue-700 hover:border-blue-300 text-gray-700 border-gray-300 bg-white
                         flex items-center justify-center gap-1"
            >
              <BarChart3 className="h-3 w-3 flex-shrink-0" />
              <span>Sales Report</span>
            </TabsTrigger>
            <TabsTrigger
              value="staff"
              className="text-xs font-medium px-3 py-2 h-10 rounded-md border-2 transition-all duration-200
                         data-[state=active]:bg-gradient-to-r data-[state=active]:from-green-500 data-[state=active]:to-green-600
                         data-[state=active]:text-white data-[state=active]:border-green-600 data-[state=active]:shadow-lg
                         hover:bg-green-50 hover:text-green-700 hover:border-green-300 text-gray-700 border-gray-300 bg-white
                         flex items-center justify-center gap-1"
            >
              <Users className="h-3 w-3 flex-shrink-0" />
              <span>Staff Performance</span>
            </TabsTrigger>
            <TabsTrigger
              value="inventory"
              className="text-xs font-medium px-3 py-2 h-10 rounded-md border-2 transition-all duration-200
                         data-[state=active]:bg-gradient-to-r data-[state=active]:from-orange-500 data-[state=active]:to-orange-600
                         data-[state=active]:text-white data-[state=active]:border-orange-600 data-[state=active]:shadow-lg
                         hover:bg-orange-50 hover:text-orange-700 hover:border-orange-300 text-gray-700 border-gray-300 bg-white
                         flex items-center justify-center gap-1"
            >
              <FileText className="h-3 w-3 flex-shrink-0" />
              <span>Inventory Report</span>
            </TabsTrigger>
            <TabsTrigger
              value="customer"
              className="text-xs font-medium px-3 py-2 h-10 rounded-md border-2 transition-all duration-200
                         data-[state=active]:bg-gradient-to-r data-[state=active]:from-purple-500 data-[state=active]:to-purple-600
                         data-[state=active]:text-white data-[state=active]:border-purple-600 data-[state=active]:shadow-lg
                         hover:bg-purple-50 hover:text-purple-700 hover:border-purple-300 text-gray-700 border-gray-300 bg-white
                         flex items-center justify-center gap-1"
            >
              <TrendingUp className="h-3 w-3 flex-shrink-0" />
              <span>Customer Analytics</span>
            </TabsTrigger>
          </TabsList>

          <TabsContent value="sales" className="space-y-6 mt-16">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Sales Breakdown */}
              <Card>
                <CardHeader className="flex flex-row items-center justify-between">
                  <CardTitle>Sales Breakdown</CardTitle>
                  <div className="flex gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleExportReport("Sales")}
                    >
                      <Download className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handlePrintReport("Sales")}
                    >
                      <Printer className="h-4 w-4" />
                    </Button>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {(selectedPeriod === "daily" ? currentData.hourlyBreakdown :
                      selectedPeriod === "weekly" ? currentData.dailyBreakdown :
                      currentData.weeklyBreakdown).map((item, index) => (
                      <div key={index} className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                        <div>
                          <p className="font-medium">{item.hour || item.day || item.week}</p>
                          <p className="text-sm text-gray-600">{item.orders} orders</p>
                        </div>
                        <div className="text-right">
                          <p className="font-bold">₹{item.revenue.toLocaleString()}</p>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              {/* Top Items */}
              <Card>
                <CardHeader>
                  <CardTitle>Top Selling Items</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {currentData.topItems?.map((item, index) => (
                      <div key={index} className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                        <div>
                          <p className="font-medium">{item.name}</p>
                          <p className="text-sm text-gray-600">{item.quantity} sold</p>
                        </div>
                        <div className="text-right">
                          <p className="font-bold">₹{item.revenue.toLocaleString()}</p>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="staff" className="space-y-6 mt-16">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between">
                <CardTitle>Staff Performance Report</CardTitle>
                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleExportReport("Staff Performance")}
                  >
                    <Download className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handlePrintReport("Staff Performance")}
                  >
                    <Printer className="h-4 w-4" />
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {staffPerformance.map((staff, index) => (
                    <div key={index} className="relative overflow-hidden bg-gradient-to-r from-white to-gray-50 border border-gray-200 rounded-xl shadow-sm hover:shadow-md transition-all duration-300">
                      <div className="p-6">
                        <div className="flex flex-col sm:flex-row sm:justify-between sm:items-start gap-6">
                          {/* Staff Info Section */}
                          <div className="flex items-center gap-4">
                            <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full flex items-center justify-center text-white font-bold text-lg shadow-lg">
                              {staff.name.split(' ').map(n => n[0]).join('')}
                            </div>
                            <div>
                              <h3 className="font-bold text-lg text-gray-900">{staff.name}</h3>
                              <div className="flex items-center gap-2">
                                <Users className="h-4 w-4 text-blue-500" />
                                <span className="text-sm font-medium text-blue-600 bg-blue-50 px-2 py-1 rounded-full">{staff.role}</span>
                              </div>
                            </div>
                          </div>

                          {/* Metrics Section */}
                          <div className="grid grid-cols-1 sm:grid-cols-3 gap-4 sm:gap-6">
                            {/* Orders/Deliveries */}
                            <div className="bg-green-50 border border-green-200 rounded-lg p-4 text-center">
                              <div className="flex items-center justify-center gap-2 mb-2">
                                <Package className="h-4 w-4 text-green-600" />
                                <span className="text-xs font-medium text-green-700">Orders/Deliveries</span>
                              </div>
                              <p className="text-2xl font-bold text-green-800">{staff.ordersServed || staff.deliveries}</p>
                            </div>

                            {/* Rating */}
                            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 text-center">
                              <div className="flex items-center justify-center gap-2 mb-2">
                                <Star className="h-4 w-4 text-yellow-600 fill-current" />
                                <span className="text-xs font-medium text-yellow-700">Rating</span>
                              </div>
                              <p className="text-2xl font-bold text-yellow-800">{staff.rating}/5</p>
                              <div className="flex justify-center mt-1">
                                {[...Array(5)].map((_, i) => (
                                  <Star
                                    key={i}
                                    className={`h-3 w-3 ${i < Math.floor(staff.rating) ? 'text-yellow-400 fill-current' : 'text-gray-300'}`}
                                  />
                                ))}
                              </div>
                            </div>

                            {/* Tips/Earnings */}
                            <div className="bg-purple-50 border border-purple-200 rounded-lg p-4 text-center">
                              <div className="flex items-center justify-center gap-2 mb-2">
                                <DollarSign className="h-4 w-4 text-purple-600" />
                                <span className="text-xs font-medium text-purple-700">Tips/Earnings</span>
                              </div>
                              <p className="text-2xl font-bold text-purple-800">₹{(staff.tips || staff.earnings).toLocaleString()}</p>
                            </div>
                          </div>
                        </div>
                      </div>

                      {/* Decorative Element */}
                      <div className="absolute top-0 right-0 w-20 h-20 bg-gradient-to-br from-blue-100 to-transparent rounded-bl-full opacity-50"></div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="inventory" className="space-y-6 mt-16">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between">
                <CardTitle>Inventory Usage Report</CardTitle>
                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleExportReport("Inventory")}
                  >
                    <Download className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handlePrintReport("Inventory")}
                  >
                    <Printer className="h-4 w-4" />
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {inventoryReport.map((item, index) => (
                    <div key={index} className="flex justify-between items-center p-4 bg-gray-50 rounded-lg">
                      <div className="flex-1">
                        <p className="font-medium">{item.item}</p>
                      </div>
                      <div className="grid grid-cols-3 gap-4 text-center">
                        <div>
                          <p className="text-sm text-gray-500">Used</p>
                          <p className="font-bold text-green-600">{item.used}</p>
                        </div>
                        <div>
                          <p className="text-sm text-gray-500">Remaining</p>
                          <p className="font-bold text-blue-600">{item.remaining}</p>
                        </div>
                        <div>
                          <p className="text-sm text-gray-500">Wastage</p>
                          <p className="font-bold text-red-600">{item.wastage}</p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="customer" className="space-y-6 mt-16">
            <Card>
              <CardHeader>
                <CardTitle>Customer Analytics</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div className="text-center p-4 bg-blue-50 rounded-lg">
                    <p className="text-2xl font-bold text-blue-600">245</p>
                    <p className="text-sm text-gray-600">Total Customers</p>
                  </div>
                  <div className="text-center p-4 bg-green-50 rounded-lg">
                    <p className="text-2xl font-bold text-green-600">89</p>
                    <p className="text-sm text-gray-600">Repeat Customers</p>
                  </div>
                  <div className="text-center p-4 bg-purple-50 rounded-lg">
                    <p className="text-2xl font-bold text-purple-600">4.6</p>
                    <p className="text-sm text-gray-600">Avg Rating</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};

export default Reports;
